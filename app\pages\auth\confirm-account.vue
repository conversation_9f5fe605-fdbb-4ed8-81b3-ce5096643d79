<script setup lang="ts">
const loading = ref(true)
const tokenVerified = ref(false)

const route = useRoute()
const { token } = route.query
const email = ref()

const { refetch } = useUserSession()

onMounted(async () => {
  if (!token) {
    loading.value = false
    return
  }

  try {
    await $fetch('/api/auth/verify-email-token', { method: 'POST', body: { token } })
    tokenVerified.value = true
    await refetch()
    toast('Logged in')
    return navigateTo(`/dashboard`, {
      replace: true,
    })
  }
  catch (error) {
    console.error('Token verification failed:', error)
    tokenVerified.value = false
  }
  finally {
    loading.value = false
  }
})

const resendVerificationEmail = async () => {
  if (!email.value) {
    // alert("Please enter your email address.");
    return
  }

  try {
    await $fetch('/api/auth/resend-verification-email', { method: 'POST', body: { email: email.value } })
    // alert("A new verification email has been sent. Please check your inbox.");
  }
  catch (error) {
    console.error('Failed to resend verification email:', error)
    // alert("Failed to resend the verification email. Please try again later.");
  }
}
</script>

<template>
  <main class="w-full max-w-md pt-8">
    <div class="flex h-screen items-center justify-center text-center gap-2 flex-col w-full p-6">
      <div class="invert dark:invert-0" style="width: 48px; height: 48px;">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 500 500"
          width="500" height="500" preserveAspectRatio="xMidYMid meet"
          style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
          <defs>
            <clipPath id="__lottie_element_1553">
              <rect width="500" height="500" x="0" y="0"></rect>
            </clipPath>
            <clipPath id="__lottie_element_1556">
              <path d="M0,0 L500,0 L500,500 L0,500z"></path>
            </clipPath>
          </defs>
          <g clip-path="url(#__lottie_element_1553)">
            <g clip-path="url(#__lottie_element_1556)" transform="matrix(1,0,0,1,0,0)" opacity="1"
              style="display: block;">
              <g class="primary design" transform="matrix(1,0,0,1,249.99899291992188,250.0030059814453)" opacity="1"
                style="display: block;">
                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                  <path stroke-linecap="round" stroke-linejoin="round" fill-opacity="0" class="primary"
                    stroke="rgb(255,255,255)" stroke-opacity="1" stroke-width="31.3"
                    d=" M192.70899963378906,151.04200744628906 C192.70899963378906,151.04200744628906 -192.70899963378906,151.04200744628906 -192.70899963378906,151.04200744628906 C-192.70899963378906,151.04200744628906 -192.70899963378906,-151.04200744628906 -192.70899963378906,-151.04200744628906 C-192.70899963378906,-151.04200744628906 192.70899963378906,-151.04200744628906 192.70899963378906,-151.04200744628906 C192.70899963378906,-151.04200744628906 192.70899963378906,151.04200744628906 192.70899963378906,151.04200744628906z">
                  </path>
                </g>
              </g>
              <g class="primary design" transform="matrix(1,0,0,1,249.99899291992188,187.5030059814453)" opacity="1"
                style="display: block;">
                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                  <path stroke-linecap="round" stroke-linejoin="round" fill-opacity="0" class="primary"
                    stroke="rgb(255,255,255)" stroke-opacity="1" stroke-width="31.3"
                    d=" M192.70899963378906,-88.54199981689453 C192.70899963378906,-88.54199981689453 45.340999603271484,62.4205436706543 45.340999603271484,62.4205436706543 C20.69499969482422,87.66759490966797 -20.69499969482422,87.66759490966797 -45.340999603271484,62.4205436706543 C-45.340999603271484,62.4205436706543 -192.70899963378906,-88.54199981689453 -192.70899963378906,-88.54199981689453">
                  </path>
                </g>
              </g>
              <g class="primary design" transform="matrix(1,0,0,1,249.99899291992188,187.5030059814453)" opacity="1"
                style="display: block;">
                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                  <path stroke-linecap="round" stroke-linejoin="round" fill-opacity="0" class="primary"
                    stroke="rgb(255,255,255)" stroke-opacity="1" stroke-width="31.3" d="M0 0"></path>
                </g>
              </g>
              <g class="primary design" transform="matrix(1,0,0,1,250,250)" opacity="1" style="display: none;">
                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                  <path class="primary" fill="rgb(255,255,255)" fill-opacity="1"
                    d=" M208.3459930419922,-151.28700256347656 C208.34100341796875,-151.5679931640625 208.32200622558594,-151.8470001220703 208.30299377441406,-152.1269989013672 C208.28599548339844,-152.3699951171875 208.2729949951172,-152.61399841308594 208.2449951171875,-152.85400390625 C208.21600341796875,-153.10499572753906 208.1719970703125,-153.35400390625 208.13099670410156,-153.60299682617188 C208.08799743652344,-153.86399841308594 208.04800415039062,-154.12600708007812 207.99200439453125,-154.3820037841797 C207.94200134277344,-154.61099243164062 207.8769989013672,-154.83599853515625 207.81700134277344,-155.06300354003906 C207.7469940185547,-155.3280029296875 207.67999267578125,-155.593994140625 207.5959930419922,-155.85299682617188 C207.5229949951172,-156.0800018310547 207.43299865722656,-156.3040008544922 207.3489990234375,-156.5279998779297 C207.25599670410156,-156.77499389648438 207.16900634765625,-157.0240020751953 207.06399536132812,-157.26499938964844 C206.96200561523438,-157.5019989013672 206.84300231933594,-157.73199462890625 206.72799682617188,-157.96400451660156 C206.61900329589844,-158.18299865722656 206.51499938964844,-158.40499877929688 206.39700317382812,-158.6179962158203 C206.26699829101562,-158.8520050048828 206.12100219726562,-159.0780029296875 205.97900390625,-159.30599975585938 C205.8520050048828,-159.50999450683594 205.72900390625,-159.71600341796875 205.59300231933594,-159.91299438476562 C205.4459991455078,-160.125 205.28599548339844,-160.3300018310547 205.1280059814453,-160.53599548339844 C204.9720001220703,-160.74000549316406 204.81700134277344,-160.94500732421875 204.6510009765625,-161.14100646972656 C204.4969940185547,-161.322998046875 204.33099365234375,-161.4969940185547 204.16799926757812,-161.67300415039062 C203.9739990234375,-161.8820037841797 203.781005859375,-162.08900451660156 203.5760040283203,-162.28700256347656 C203.51699829101562,-162.343994140625 203.46600341796875,-162.406005859375 203.406005859375,-162.46200561523438 C203.2949981689453,-162.5659942626953 203.17599487304688,-162.65199279785156 203.06300354003906,-162.7519989013672 C202.84800720214844,-162.94200134277344 202.6320037841797,-163.12899780273438 202.40699768066406,-163.3070068359375 C202.2169952392578,-163.45799255371094 202.02200317382812,-163.60000610351562 201.82699584960938,-163.74099731445312 C201.61000061035156,-163.89700317382812 201.39199829101562,-164.0489959716797 201.16700744628906,-164.19400024414062 C200.95399475097656,-164.33099365234375 200.73800659179688,-164.46099853515625 200.52000427246094,-164.58700561523438 C200.2989959716797,-164.71499633789062 200.0760040283203,-164.83799743652344 199.84800720214844,-164.9550018310547 C199.6199951171875,-165.07200622558594 199.38999938964844,-165.1820068359375 199.1580047607422,-165.28700256347656 C198.92599487304688,-165.39199829101562 198.69200134277344,-165.49200439453125 198.45399475097656,-165.58599853515625 C198.218994140625,-165.6790008544922 197.98300170898438,-165.76499938964844 197.74400329589844,-165.8459930419922 C197.5,-165.9290008544922 197.2550048828125,-166.0070037841797 197.00599670410156,-166.0780029296875 C196.76300048828125,-166.14700317382812 196.51800537109375,-166.20899963378906 196.27200317382812,-166.26600646972656 C196.0229949951172,-166.32400512695312 195.7740020751953,-166.3780059814453 195.52099609375,-166.4239959716797 C195.26199340820312,-166.4709930419922 195.0019989013672,-166.50900268554688 194.74099731445312,-166.54299926757812 C194.49899291992188,-166.57400512695312 194.2570037841797,-166.60400390625 194.01100158691406,-166.62399291992188 C193.72900390625,-166.64700317382812 193.4459991455078,-166.65899658203125 193.16299438476562,-166.66700744628906 C193.00999450683594,-166.67100524902344 192.86199951171875,-166.69000244140625 192.70799255371094,-166.69000244140625 C192.70799255371094,-166.69000244140625 -192.7100067138672,-166.69000244140625 -192.7100067138672,-166.69000244140625 C-192.86000061035156,-166.69000244140625 -193.00599670410156,-166.67100524902344 -193.15499877929688,-166.66700744628906 C-193.44400024414062,-166.65899658203125 -193.73199462890625,-166.64700317382812 -194.02000427246094,-166.6230010986328 C-194.25999450683594,-166.60299682617188 -194.49600219726562,-166.5749969482422 -194.73300170898438,-166.54400634765625 C-195,-166.50900268554688 -195.26600646972656,-166.4709930419922 -195.531005859375,-166.42300415039062 C-195.7779998779297,-166.3780059814453 -196.0229949951172,-166.3249969482422 -196.26600646972656,-166.26800537109375 C-196.51699829101562,-166.2100067138672 -196.76600646972656,-166.14700317382812 -197.01499938964844,-166.0760040283203 C-197.25900268554688,-166.00599670410156 -197.49899291992188,-165.92999267578125 -197.73800659179688,-165.8489990234375 C-197.9810028076172,-165.76600646972656 -198.22300720214844,-165.6790008544922 -198.46299743652344,-165.58399963378906 C-198.6959991455078,-165.49200439453125 -198.9250030517578,-165.39300537109375 -199.1529998779297,-165.2899932861328 C-199.38999938964844,-165.18299865722656 -199.62399291992188,-165.0709991455078 -199.8560028076172,-164.95199584960938 C-200.0800018310547,-164.83700561523438 -200.2989959716797,-164.71600341796875 -200.51600646972656,-164.59100341796875 C-200.73800659179688,-164.46299743652344 -200.95700073242188,-164.33099365234375 -201.1739959716797,-164.1909942626953 C-201.39599609375,-164.04800415039062 -201.61099243164062,-163.8979949951172 -201.8249969482422,-163.74400329589844 C-202.0229949951172,-163.6020050048828 -202.21800231933594,-163.45899963378906 -202.41099548339844,-163.30599975585938 C-202.63499450683594,-163.12899780273438 -202.85000610351562,-162.94200134277344 -203.06399536132812,-162.7530059814453 C-203.177001953125,-162.6529998779297 -203.2969970703125,-162.5659942626953 -203.4080047607422,-162.46200561523438 C-203.46800231933594,-162.406005859375 -203.5189971923828,-162.343994140625 -203.5780029296875,-162.28700256347656 C-203.7830047607422,-162.08900451660156 -203.9759979248047,-161.8820037841797 -204.1699981689453,-161.67300415039062 C-204.33299255371094,-161.4969940185547 -204.49899291992188,-161.322998046875 -204.6529998779297,-161.14100646972656 C-204.81900024414062,-160.94500732421875 -204.9739990234375,-160.74000549316406 -205.1300048828125,-160.53599548339844 C-205.28799438476562,-160.3300018310547 -205.447998046875,-160.125 -205.59500122070312,-159.91299438476562 C-205.7310028076172,-159.71600341796875 -205.85400390625,-159.50999450683594 -205.9810028076172,-159.30599975585938 C-206.1230010986328,-159.0780029296875 -206.27000427246094,-158.8520050048828 -206.3990020751953,-158.6179962158203 C-206.51800537109375,-158.4040069580078 -206.62100219726562,-158.1820068359375 -206.72999572753906,-157.96200561523438 C-206.843994140625,-157.72999572753906 -206.96400451660156,-157.50100708007812 -207.0659942626953,-157.26499938964844 C-207.17100524902344,-157.0229949951172 -207.25900268554688,-156.7740020751953 -207.3520050048828,-156.5260009765625 C-207.43600463867188,-156.302001953125 -207.52499389648438,-156.0800018310547 -207.59800720214844,-155.85299682617188 C-207.6820068359375,-155.593994140625 -207.74899291992188,-155.3280029296875 -207.81900024414062,-155.06300354003906 C-207.87899780273438,-154.83599853515625 -207.94400024414062,-154.61099243164062 -207.99400329589844,-154.3820037841797 C-208.0500030517578,-154.12600708007812 -208.08999633789062,-153.86399841308594 -208.13299560546875,-153.60299682617188 C-208.1739959716797,-153.35400390625 -208.21800231933594,-153.10499572753906 -208.2469940185547,-152.85400390625 C-208.27499389648438,-152.61399841308594 -208.28799438476562,-152.3699951171875 -208.30499267578125,-152.1269989013672 C-208.32400512695312,-151.8470001220703 -208.34300231933594,-151.5679931640625 -208.34800720214844,-151.28700256347656 C-208.3489990234375,-151.20399475097656 -208.36000061035156,-151.12399291992188 -208.36000061035156,-151.0399932861328 C-208.36000061035156,-151.0399932861328 -208.36000061035156,151.0449981689453 -208.36000061035156,151.0449981689453 C-208.36000061035156,159.68800354003906 -201.35400390625,166.69500732421875 -192.7100067138672,166.69500732421875 C-192.7100067138672,166.69500732421875 192.70799255371094,166.69500732421875 192.70799255371094,166.69500732421875 C201.3520050048828,166.69500732421875 208.35800170898438,159.68800354003906 208.35800170898438,151.0449981689453 C208.35800170898438,151.0449981689453 208.35800170898438,-151.0399932861328 208.35800170898438,-151.0399932861328 C208.35800170898438,-151.12399291992188 208.3470001220703,-151.20399475097656 208.3459930419922,-151.28700256347656z M156.60800170898438,-135.38999938964844 C156.60800170898438,-135.38999938964844 33.91699981689453,-4.389999866485596 33.91699981689453,-4.389999866485596 C24.988000869750977,5.144000053405762 12.940999984741211,10.395000457763672 -0.0020000000949949026,10.395000457763672 C-12.944999694824219,10.395000457763672 -24.989999771118164,5.144000053405762 -33.91899871826172,-4.389999866485596 C-33.91899871826172,-4.389999866485596 -156.61000061035156,-135.38999938964844 -156.61000061035156,-135.38999938964844 C-156.61000061035156,-135.38999938964844 156.60800170898438,-135.38999938964844 156.60800170898438,-135.38999938964844z M-177.05999755859375,135.39500427246094 C-177.05999755859375,135.39500427246094 -177.05999755859375,-111.43499755859375 -177.05999755859375,-111.43499755859375 C-177.05999755859375,-111.43499755859375 -56.76499938964844,17.006000518798828 -56.76499938964844,17.006000518798828 C-41.85499954223633,32.92599868774414 -21.69499969482422,41.69499969482422 -0.0020000000949949026,41.69499969482422 C-0.0010000000474974513,41.69499969482422 -0.003000000026077032,41.69499969482422 -0.0010000000474974513,41.69499969482422 C21.69099998474121,41.69499969482422 41.85200119018555,32.92599868774414 56.76300048828125,17.006999969482422 C56.76300048828125,17.006999969482422 177.05799865722656,-111.43499755859375 177.05799865722656,-111.43499755859375 C177.05799865722656,-111.43499755859375 177.05799865722656,135.39500427246094 177.05799865722656,135.39500427246094 C177.05799865722656,135.39500427246094 -177.05999755859375,135.39500427246094 -177.05999755859375,135.39500427246094z">
                  </path>
                </g>
              </g>
              <g class="primary design" style="display: none;">
                <g>
                  <path class="primary"></path>
                </g>
              </g>
            </g>
          </g>
        </svg>
      </div>
      <h2 class="text-xl tracking-[-0.16px] text-slate-12 font-bold">Check your email</h2>
      <span class="text-sm text-slate-11 font-normal">We just sent a verification <NAME_EMAIL>.</span>
      <button
        class="font-semibold inline-flex items-center justify-center border select-none relative cursor-pointer disabled:cursor-not-allowed disabled:opacity-70 transition ease-in-out duration-200 bg-black dark:bg-white text-white dark:text-black border-slate-6 hover:bg-black/90 dark:hover:bg-white/90 focus-visible:ring-2 dark:focus-visible:ring-white/40 focus-visible:ring-black/40 focus-visible:outline-none dark:focus-visible:bg-white/90 focus-visible:bg-black/90 disabled:hover:bg-black dark:disabled:hover:bg-white text-sm h-8 pl-3 pr-3 rounded-md gap-1 mt-6"><span
          class="absolute w-full inset-0 flex items-center justify-center invisible">
          <span class="inline-flex items-center gap-1">
            <span class="h-1 w-1 animate-plop rounded-full bg-root"></span>
            <span class="h-1 w-1 animate-plop2 rounded-full bg-root"></span>
            <span class="h-1 w-1 animate-plop3 rounded-full bg-root"></span>
          </span>
        </span>
        <span class="inline-flex items-center justify-center gap-1 truncate visible">Go to login</span>
        <span class="text-[#70757E] visible">
          <div class="invert dark:invert-0" style="width: 16px; height: 16px;">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 500 500"
              width="500" height="500" preserveAspectRatio="xMidYMid meet"
              style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
              <defs>
                <clipPath id="__lottie_element_1575">
                  <rect width="500" height="500" x="0" y="0"></rect>
                </clipPath>
                <clipPath id="__lottie_element_1578">
                  <path d="M0,0 L500,0 L500,500 L0,500z"></path>
                </clipPath>
              </defs>
              <g clip-path="url(#__lottie_element_1575)">
                <g clip-path="url(#__lottie_element_1578)" transform="matrix(1,0,0,1,0,0)" opacity="1"
                  style="display: block;">
                  <g class="primary design" transform="matrix(1,0,0,1,731.0029907226562,250.0030059814453)" opacity="1"
                    style="display: block;">
                    <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                      <path class="primary" fill="rgb(18,19,48)" fill-opacity="1"
                        d=" M203.85299682617188,-10.98799991607666 C203.85299682617188,-10.98799991607666 121.68599700927734,-94.3219985961914 121.68599700927734,-94.3219985961914 C115.62100219726562,-100.4749984741211 105.70999908447266,-100.5459976196289 99.55400085449219,-94.47799682617188 C93.4000015258789,-88.41000366210938 93.33000183105469,-78.5 99.39900207519531,-72.34600067138672 C99.39900207519531,-72.34600067138672 155.3000030517578,-15.649999618530273 155.3000030517578,-15.649999618530273 C155.3000030517578,-15.649999618530273 -192.70899963378906,-15.649999618530273 -192.70899963378906,-15.649999618530273 C-201.35299682617188,-15.649999618530273 -208.36000061035156,-8.642999649047852 -208.36000061035156,0 C-208.36000061035156,8.642999649047852 -201.35299682617188,15.64900016784668 -192.70899963378906,15.64900016784668 C-192.70899963378906,15.64900016784668 155.30099487304688,15.64900016784668 155.30099487304688,15.64900016784668 C155.30099487304688,15.64900016784668 99.39900207519531,72.34600067138672 99.39900207519531,72.34600067138672 C93.33000183105469,78.5 93.4000015258789,88.41000366210938 99.55400085449219,94.47799682617188 C102.60399627685547,97.48400115966797 106.572998046875,98.98300170898438 110.54199981689453,98.98300170898438 C114.58399963378906,98.98300170898438 118.6240005493164,97.4260025024414 121.68599700927734,94.32099914550781 C121.68599700927734,94.32099914550781 203.85299682617188,10.987000465393066 203.85299682617188,10.987000465393066 C209.86199951171875,4.894000053405762 209.86199951171875,-4.894999980926514 203.85299682617188,-10.98799991607666z">
                      </path>
                    </g>
                  </g>
                  <g class="primary design" transform="matrix(1,0,0,1,249.9459228515625,250.0030059814453)" opacity="1"
                    style="display: block;">
                    <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                      <path class="primary" fill="rgb(18,19,48)" fill-opacity="1"
                        d=" M203.85299682617188,-10.98799991607666 C203.85299682617188,-10.98799991607666 121.68599700927734,-94.3219985961914 121.68599700927734,-94.3219985961914 C115.62100219726562,-100.4749984741211 105.70999908447266,-100.5459976196289 99.55400085449219,-94.47799682617188 C93.4000015258789,-88.41000366210938 93.33000183105469,-78.5 99.39900207519531,-72.34600067138672 C99.39900207519531,-72.34600067138672 155.3000030517578,-15.649999618530273 155.3000030517578,-15.649999618530273 C155.3000030517578,-15.649999618530273 -192.70899963378906,-15.649999618530273 -192.70899963378906,-15.649999618530273 C-201.35299682617188,-15.649999618530273 -208.36000061035156,-8.642999649047852 -208.36000061035156,0 C-208.36000061035156,8.642999649047852 -201.35299682617188,15.64900016784668 -192.70899963378906,15.64900016784668 C-192.70899963378906,15.64900016784668 155.30099487304688,15.64900016784668 155.30099487304688,15.64900016784668 C155.30099487304688,15.64900016784668 99.39900207519531,72.34600067138672 99.39900207519531,72.34600067138672 C93.33000183105469,78.5 93.4000015258789,88.41000366210938 99.55400085449219,94.47799682617188 C102.60399627685547,97.48400115966797 106.572998046875,98.98300170898438 110.54199981689453,98.98300170898438 C114.58399963378906,98.98300170898438 118.6240005493164,97.4260025024414 121.68599700927734,94.32099914550781 C121.68599700927734,94.32099914550781 203.85299682617188,10.987000465393066 203.85299682617188,10.987000465393066 C209.86199951171875,4.894000053405762 209.86199951171875,-4.894999980926514 203.85299682617188,-10.98799991607666z">
                      </path>
                    </g>
                  </g>
                </g>
              </g>
            </svg>
          </div>
        </span>
      </button>
    </div>
  </main>
</template>
