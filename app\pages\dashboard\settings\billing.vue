<script setup lang="ts">
const { user, clear, loggedIn, refreshSession } = useUserSession()

const {
  data: subscriptions,
  status,
  error,
  refresh,
} = await useFetch('/api/payment/subscription')
const { data: products } = await useFetch('/api/payment/products')

const loading = ref(false)
const getPortalLink = async (id, flow) => {
  try {
    loading.value = true
    const billingPortalLink = await $fetch('/api/payment/billing-portal-link', {
      query: { id, flow },
    })
    window.location.href = billingPortalLink
  }
  catch (error) {
    loading.value = false
    toast.error('Error creating billing portal link')
  }
}

const getProduct = (productId) => {
  return products.value?.find(v => v.id === productId)
}

const getVariant = (productId, variantId) => {
  const product = getProduct(productId)
  const variant = (product?.variants ?? []).find(v => v.id === variantId)
  return variant
}
</script>

<template>
  <section
    id="billing"
    class="mx-auto w-full max-w-full px-6 md:max-w-5xl"
  >
    <div class="flex justify-between py-8">
      <h1 class="flex items-center text-[28px] font-bold leading-[34px] tracking-[-0.416px]">
        Settings
        <Icon
          v-if="status === 'pending'"
          name="svg-spinners:3-dots-fade"
          class="ml-2 size-5"
        />
      </h1>
    </div>

    <DashboardTabNavs />

    <div
      v-if="status === 'success' && subscriptions.length === 0"
      class="flex h-80 flex-col items-center justify-center gap-8 rounded-lg border p-6"
    >
      <div class="flex max-w-md flex-col gap-2 text-center">
        <h2 class="text-xl font-bold tracking-[-0.16px]">
          You haven't any subscriptions yet
        </h2>
        <p class="text-sm text-muted-foreground">
          Once you have successfully created a subscription, you'll get detailed information about the subscription, as well as upgrade or downgrade it as needed.
        </p>
      </div>
      <Button class="font-semibold">
        <NuxtLink href="/pricing">
          Upgrade
        </NuxtLink>
      </Button>
    </div>

    <div
      v-if="subscriptions.length > 0"
      class="flex flex-col gap-6"
      style="opacity: 1; transform: none;"
    >
      <section class="rounded-lg border">
        <div class="border-b px-6 py-4">
          <h2 class="text-xl font-bold tracking-[-0.16px]">
            Subscriptions
          </h2>
        </div>
        <div class="relative w-full overflow-x-auto overflow-y-hidden">
          <Table>
            <TableBody>
              <TableRow
                v-for="row in subscriptions"
                :key="row.id"
              >
                <TableCell class="pl-6 font-medium">
                  {{ getProduct(row.productId)?.name }}
                </TableCell>
                <TableCell class="hidden md:table-cell">
                  ${{ getVariant(row.productId, row.variantId)?.price / 100 }} / {{ getVariant(row.productId, row.variantId)?.interval }}
                </TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    size="xs"
                  >
                    {{ row.status }}
                  </Badge>
                </TableCell>
                <TableCell class="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger as-child>
                      <Button
                        aria-haspopup="true"
                        size="icon"
                        variant="ghost"
                      >
                        <Icon name="radix-icons:dots-horizontal" />
                        <span class="sr-only">Toggle menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem @click="getPortalLink(row.id, 'update-plan')">
                        Manage Billing
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="getPortalLink(row.id, 'update-payment-info')">
                        Update Payment Info
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="getPortalLink(row.id, 'cancel')">
                        Cancel Subscription
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
        <div class="flex justify-between border-t py-3 pl-5 pr-3">
          <Button>Upgrade</Button>
        </div>
      </section>
    </div>
  </section>
</template>
