<script setup lang="ts">
const loading = ref(true)
const tokenVerified = ref(false)

const route = useRoute()
const { token } = route.query
const email = ref()

const { refreshSession } = useUserSession()

onMounted(async () => {
  if (!token) {
    loading.value = false
    return
  }

  try {
    await $fetch('/api/auth/verify-email-token', { method: 'POST', body: { token } })
    tokenVerified.value = true
    await refreshSession()
    toast('Logged in')
    return navigateTo(`/dashboard`, {
      replace: true,
    })
  }
  catch (error) {
    console.error('Token verification failed:', error)
    tokenVerified.value = false
  }
  finally {
    loading.value = false
  }
})

const resendVerificationEmail = async () => {
  if (!email.value) {
    // alert("Please enter your email address.");
    return
  }

  try {
    await $fetch('/api/auth/resend-verification-email', { method: 'POST', body: { email: email.value } })
    // alert("A new verification email has been sent. Please check your inbox.");
  }
  catch (error) {
    console.error('Failed to resend verification email:', error)
    // alert("Failed to resend the verification email. Please try again later.");
  }
}
</script>

<template>
  <div class="flex items-center justify-center py-8">
    <div v-if="loading">
      Checking email verification status...
    </div>

    <div v-if="!loading && !tokenVerified">
      <p>The verification link is incorrect or has expired. Please request a new one.</p>
      <input
        v-model="email"
        type="email"
        placeholder="Enter your email"
      >
      <button @click="resendVerificationEmail">
        Resend Verification Email
      </button>
    </div>

    <div v-if="tokenVerified">
      <p>Email verification successful!</p>
    </div>
  </div>
</template>
