import { authClient } from '@/utils/auth-client'

// 基于 better-auth 的方法，兼容了 nuxt-auth-utils 的 useUserSession，避免修改太多代码
export function useUserSession() {
  const session = authClient.useSession()

  // 计算登录状态
  const loggedIn = computed(() => !!session.value?.data?.user)

  // 用户信息
  const user = computed(() => session.value?.data?.user || null)

  // 清除会话（登出）
  const clear = async () => {
    await authClient.signOut()
  }

  // 刷新会话数据
  const refreshSession = async () => {
    try {
      // 直接使用 authClient 重新获取 session
      await authClient.getSession({
        fetchOptions: {
          cache: 'no-cache'
        }
      })
      console.log('Session refreshed successfully')
    } catch (error) {
      console.warn('Failed to refresh session:', error)
    }
  }

  return {
    // 会话状态
    loggedIn: readonly(loggedIn),
    user: readonly(user),

    // 方法
    refreshSession,
    clear,

    // 原始会话对象
    session
  }
}