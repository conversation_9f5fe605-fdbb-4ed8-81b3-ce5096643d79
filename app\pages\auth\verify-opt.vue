<script setup lang="ts">
const route = useRoute()
const { email } = route.query

const loading = ref(false)
const opt = ref()

const { refreshSession } = useUserSession()
const handleComplete = async () => {
  try {
    const optValue = opt.value.join('')
    loading.value = true
    await $fetch('/api/auth/verify-opt', { method: 'POST', body: { email, opt: optValue } })
    await refreshSession()
    toast('Logged in')
    return navigateTo(`/dashboard`, {
      replace: true,
    })
  }
  catch (error) {
    toast.error(error.data?.statusMessage || error.data?.message || 'An unknown error occurred')
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <Button
    as-child
    variant="ghost"
    class="absolute left-0 top-6 rounded-full text-sm font-semibold text-muted-foreground md:left-6"
  >
    <NuxtLink href="/">
      <Icon
        name="radix-icons:caret-left"
        class="mr-1"
      /> Home
    </NuxtLink>
  </Button>
  <div class="flex h-screen items-center justify-center">
    <main class="mx-auto min-h-[490px] w-full max-w-[450px]">
      <Icon name="lucide:mail" class="w-10 h-10" />
      <h1 class="mb-1.5 mt-8 text-xl font-bold">
        Check your email
      </h1>
      <p class="mb-8 text-base font-normal text-muted-foreground">
        We just sent a code to {{ email }}.
        <br>
        If you can't find the email, check your spam folder.
      </p>
      
      <PinInput
        id="pin-input"
        v-model="opt"
        placeholder="○"
        @complete="handleComplete"
        class="mt-10"
      >
        <PinInputGroup class="gap-2">
          <PinInputSlot
            v-for="(id, index) in 6"
            :key="id"
            :index="index"
            class="text-lg w-12 h-12 border rounded-md"
          />
        </PinInputGroup>
      </PinInput>
    </main>
  </div>
</template>
