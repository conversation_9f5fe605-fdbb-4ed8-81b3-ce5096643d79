<script setup lang="ts">
import { authClient } from '@/utils/auth-client'
const route = useRoute()

const { brand } = useRuntimeConfig().public

const { user } = useUserSession()
const logOut = async () => {
  await authClient.signOut({
    fetchOptions: {
      onSuccess: () => {
        navigateTo('/')
      },
    },
  });
}

const menu = [

  {
    title: 'Images',
    url: '/dashboard/images',
    icon: 'lucide:images',
  },
  {
    title: 'Settings',
    url: '/dashboard/settings',
    icon: 'lucide:settings-2',
  },
]

const mobileMenuVisible = ref(false)
const toggleMobileMenu = () => {
  mobileMenuVisible.value = !mobileMenuVisible.value
}

const isActiveMenuItem = (href: string | null) => {
  return href && route.path.includes(href)
}



onMounted(async () => {
  console.log('layout dashboard mounted')
});
</script>

<template>
  <div class="flex">
    <aside class="hidden h-screen w-[250px] shrink-0 flex-col justify-between border-r px-4 pb-6 md:flex">
      <!-- menu -->
      <div class="flex h-[60px] items-center">
        <NuxtLink
          class="text-lg font-semibold tracking-tight"
          href="/"
        >
          {{ brand }}
        </NuxtLink>
      </div>

      <div class="mb-6 space-y-4">




        <div>
          <div class="flex items-center justify-between gap-2">
            <h3 class="text-xs font-semibold uppercase text-muted-foreground">Snapshotss</h3>
            <!-- <Button variant="ghost" size="icon"  @click="newFolderDialogOpen = true">
              <Icon name="lucide:plus" />
            </Button> -->
          </div>
          <NuxtLink :to="`/dashboard/snapshots`">
            <Button variant="outline">Snapshots</Button>
          </NuxtLink>
        </div>
      </div>
      
      <nav class="-ml-2 mt-6 flex-1">
        <ul class="flex flex-col gap-2">
          <li
            v-for="item in menu"
            :key="item.title"
          >
            <Button
              as-child
              variant="ghost"
              size="sm"
              class="justify-start text-base"
            >
              <NuxtLink
                class="flex h-8 w-full gap-2 text-sm"
                :href="item.url"
                :class="isActiveMenuItem(item.url) ? 'bg-secondary' : 'text-muted-foreground'"
              >
                <Icon
                  :name="item.icon"
                  class="opacity-80"
                />
                {{ item.title }}
                <span class="ml-auto" />
              </NuxtLink>
            </Button>
          </li>
        </ul>
      </nav>

      <!-- user dropdown -->
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button
            variant="ghost"
            class="flex h-11 w-full items-center gap-2 px-1 data-[state=open]:bg-accent data-[state=open]:text-accent-foreground"
          >
            <Avatar class="size-8 rounded-lg">
              <AvatarImage
                :src="user.image || ''"
                :alt="user.name"
              />
              <AvatarFallback class="rounded-lg">
                {{ avatarName(user.name || '') }}
              </AvatarFallback>
            </Avatar>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-semibold">{{ user.name }}</span>
              <span class="truncate text-xs text-muted-foreground">{{ user.email }}</span>
            </div>
            <Icon name="radix-icons:caret-sort" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-[--radix-dropdown-menu-trigger-width] min-w-56"
          side="bottom"
          align="center"
          :side-offset="4"
        >
          <DropdownMenuLabel class="p-0 font-normal">
            <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
              <Avatar class="size-8 rounded-lg">
                <AvatarImage
                  :src="user.image || ''"
                  :alt="user.name"
                />
                <AvatarFallback class="rounded-lg">
                  {{ avatarName(user.name || '') }}
                </AvatarFallback>
              </Avatar>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">{{ user.name }}</span>
                <span class="truncate text-xs">{{ user.email }}</span>
              </div>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem as-child>
            <NuxtLink href="/">
              <Icon
                name="lucide:slash"
                class="mr-2"
              /> Home
            </NuxtLink>
          </DropdownMenuItem>
          <DropdownMenuItem as-child>
            <NuxtLink href="/dashboard">
              <Icon
                name="lucide:layout-dashboard"
                class="mr-2"
              /> Dashboard
            </NuxtLink>
          </DropdownMenuItem>
          <DropdownMenuItem
            as-child
            :disabled="user.role !== 'ADMIN'"
          >
            <NuxtLink href="/admin">
              <Icon
                name="lucide:chart-scatter"
                class="mr-2"
              /> Admin
            </NuxtLink>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem as-child>
              <NuxtLink href="/pricing">
                <span class="mr-2 size-4">✨</span> Upgrade to Pro
              </NuxtLink>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem as-child>
              <NuxtLink href="/dashboard/settings">
                Account
              </NuxtLink>
            </DropdownMenuItem>
            <DropdownMenuItem as-child>
              <NuxtLink href="/dashboard/settings/billing">
                Billing
              </NuxtLink>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem @click="logOut">
            <Icon
              name="lucide:log-out"
              class="mr-2"
            />
            Log out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </aside>
    <div class="w-full">
      <div class="flex h-[60px] items-center justify-end px-6 md:border-b">
        <!-- mobile menu -->
        <div class="absolute left-0 top-0 z-40 flex w-full flex-col items-center bg-background md:hidden">
          <div class="flex h-[72px] w-full items-center justify-between px-6">
            <NuxtLink
              class="text-lg font-semibold"
              href="/"
            >
              {{ brand }}
            </NuxtLink>
            <Button
              variant="ghost"
              class="my-0 px-2"
              @click="toggleMobileMenu"
            >
              <span class="sr-only">Open main menu</span>
              <Icon
                v-if="mobileMenuVisible"
                name="radix-icons:cross-1"
                class="size-6"
              />
              <Icon
                v-else
                name="radix-icons:hamburger-menu"
                class="size-6"
              />
            </Button>
          </div>
          <div
            v-show="mobileMenuVisible"
            id="mobile-menu"
            class="h-[calc(100vh-72px)] w-full flex-col bg-background px-6 pt-6"
          >
            <a
              v-for="item in menu"
              :key="item.title"
              class="block w-full border-b py-4 font-semibold text-muted-foreground last:border-none hover:text-foreground"
              :href="item.url"
            >{{ item.title }}
            </a>
            <Button
              variant="ghost"
              class="-ml-3 mb-5 mt-12 text-base text-muted-foreground"
            >
              <Icon
                name="lucide:log-out"
                class="mr-2 text-sm"
              />
              <span class="font-semibold">Log out</span>
            </Button>
          </div>
        </div>
        <!-- page header -->
        <div class="hidden items-center gap-4 text-sm text-muted-foreground md:flex">
          <Button
            variant="outline"
            class=""
          >
            <Icon
              name="lucide:headphones"
              class="mr-2"
            /> Feedback
          </Button>
          <NuxtLink
            href="#"
            class="font-semibold"
          >
            Help
          </NuxtLink>
          <NuxtLink
            href="#"
            class="font-semibold"
          >
            Docs <Icon name="lucide:arrow-up-right" /></NuxtLink>
        </div>
      </div>
      <!-- page content -->
      <div class="h-[calc(100vh-70px)] overflow-auto pb-10">
        <slot />
      </div>
    </div>


  </div>
</template>
