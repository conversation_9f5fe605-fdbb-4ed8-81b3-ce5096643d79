/**
 * 获取用户加密密钥配置的 API 端点
 * 仅在用户需要使用加密功能时调用，提供按需访问的安全性
 */
export default defineEventHandler(async (event) => {
  try {
    const { user } = await requireUserSession(event)

    // 只选择加密相关字段，提高安全性
    const userData = await db.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        encryptSalt: true,
        encryptKey: true,
        encryptIv: true,
        encryptAlgo: true,
      }
    })

    if (!userData) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found',
      })
    }

    // 检查用户是否已设置加密密钥
    const hasEncryptionSetup = !!(
      userData.encryptSalt &&
      userData.encryptKey &&
      userData.encryptIv
    )

    if (!hasEncryptionSetup) {
      return {
        hasEncryptionSetup: false,
        message: 'Encryption not configured for this user'
      }
    }

    // 返回加密配置
    return {
      hasEncryptionSetup: true,
      encryptSalt: userData.encryptSalt,
      encryptKey: userData.encryptKey,
      encryptIv: userData.encryptIv,
      encryptAlgo: userData.encryptAlgo || 'AES-GCM-128'
    }
  } catch (error) {
    // 记录错误但不暴露敏感信息
    console.error('Error fetching encryption keys:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch encryption configuration',
    })
  }
})
